package com.integral.orderbook.view;

import com.integral.commons.counter.AtomicCounter;
import com.integral.is.common.ISConstantsC;
import com.integral.is.common.mbean.ISFactory;
import com.integral.is.common.query.ClientDataQueryConstants;
import com.integral.is.common.query.ClientDataQueryHelper;
import com.integral.is.common.util.JMSProxyUtil;
import com.integral.is.oms.*;
import com.integral.is.spaces.fx.client.fxi.ClientResponseBuilder;
import com.integral.is.spaces.fx.esp.logger.FXESPWorkflowLogger;
import com.integral.is.system.notification.NotificationMessageSender;
import com.integral.log.Log;
import com.integral.log.LogFactory;
import com.integral.model.dealing.OrderRequest;
import com.integral.model.dealing.SingleLegOrder;
import com.integral.user.Organization;
import com.integral.user.User;
import com.integral.util.collections.ConcurrentHashSet;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.LockSupport;

public class OrderBookViewNotifier implements Runnable{
	
	private Log log = LogFactory.getLog(this.getClass());

	private static final int ORDER_BOOK_VIEW_MSG_PRIORITY = 10; //TODO:expose it via property?
	private static final String ORDER_BOOK_VIEW_HANDLER = "OBV";
	private static Map<String, String> jmsProps = new HashMap<String, String>(2);
	static {
		jmsProps.put( ISConstantsC.JMS_PROP_ACK_REQUIRED, "true" );
		jmsProps.put( "MessageType", "ResponseESP_1" );
	}

	public void notifyAllSubscribedOrg() {
		// get the list of orgs having atleast one subscribed user
		Enumeration<String> activeOrgList = OrderBookSubscriptionService
				.getInstance().getOrganizationsWithActiveSubscriptions();
		while (activeOrgList.hasMoreElements()) {
			String orgKey = activeOrgList.nextElement();
			// retrieve active users for each org
			ConcurrentHashSet<String> activeUsers = OrderBookSubscriptionService
					.getInstance().getSubscribedUsersForOrg(orgKey);
			if (activeUsers != null && !activeUsers.isEmpty()) {
				notifySubscribedUsersOfOrg(orgKey, activeUsers);
			}
		}
	}

	private void notifySubscribedUsersOfOrg(String orgKey,
			ConcurrentHashSet<String> subscribedUsers) {
		// get the order books belonging to this org
		List<OrderBook> orderBooks = OrderBookCacheC.getInstance()
				.findOrderBooksByOrganizationShortname(orgKey);
		for (OrderBook ob : orderBooks) {
			sendUpdatesForOrderBook(ob, subscribedUsers);
		}
	}
	
	private void sendUpdatesForOrderBook(OrderBook ob, ConcurrentHashSet<String> subscribedUsers){
		//get the corresponding orderbook view
		//OrderBookView obv = OrderBookViewUtil.getOrderBookView(orderBook);

		//Iterator<OrderView> orderViewItr = obv.getOrderViews().iterator();
		//while (orderViewItr.hasNext()) {

		//Currently iterating on pegged orders
		for(Map.Entry<String, Order>orderItr : ((OrderBookC)ob).getPeggedOrders().entrySet()){
			//OrderView ov = orderViewItr.next();
			OrderView ov = ((OrderC)orderItr.getValue()).getOrderView();
			String ovUserName = ov.getUser().getShortName();
			//check if order view belongs to one of the subscribed users
			if (subscribedUsers.contains(ovUserName)) {
				//send only if OV is updated since the last flush
				if(ov.isLatestSinceLastFlush()){
					logMessage("Sending update about pegged order view:" + ov);
					publishOrderView(ov.generateOrderViewResponse());
				}
			}
		}
	}

	/**
	 * Sends updates for trailing stop orders to subscribed users
	 *
	 */
	private void sendTrailingStopOrderUpdates() {
		// Get all trailing stop orders from the cache
		if(OrderBookSubscriptionService.getInstance().getTrailingStopOrderCount() > 0) {
			for (Order trailingStopOrder : OrderBookSubscriptionService.getInstance().getTrailingStopOrdersCache().values()) {
				if (trailingStopOrder != null) {
					long stopPriceUpdateCount = trailingStopOrder.getEntityDescriptor().getStopPriceUpdateCount();
					if (stopPriceUpdateCount > trailingStopOrder.getEntityDescriptor().getLastPersistedStopPriceUpdateCount()) {
						/*
							Limit the number of persistence events being generated by using the modified time of the persistence entity
						 */
						long trailingStopTriggerUpdatePersistenceInterval = OrderBookViewAttributeMBeanC.getInstance().getTrailingStopTriggerUpdatePersistenceInterval();
						OrderRequest orderRequest = (OrderRequest) trailingStopOrder.getEntityDescriptor().getEntity();
						if(System.currentTimeMillis()-orderRequest.getModifiedTime()>trailingStopTriggerUpdatePersistenceInterval){
							trailingStopOrder.getEntityDescriptor().persistEntityOnTriggerUpdate();
						}
					}
					if( stopPriceUpdateCount > trailingStopOrder.getEntityDescriptor().getLastNotifiedStopPriceUpdateCount()){
						/*
							Notify updated stop price for a trailing stop order
						 */
						SingleLegOrder orderRequest = (SingleLegOrder) trailingStopOrder.getEntityDescriptor().getEntity();
						Map<String,Object> orderData = OrderBookSubscriptionService.getInstance().getTrailingStopOrderData(trailingStopOrder.getOrderId());
						if( orderData == null ){
							orderData = new ConcurrentHashMap<String, Object>();
							ClientResponseBuilder.getInstance().updateData(orderRequest,orderData);
							/*
								populate the static data
							 */
							OrderBookSubscriptionService.getInstance().setTrailingStopOrderData(trailingStopOrder.getOrderId(), orderData);
						}
						else{
							ClientResponseBuilder.getInstance().updateData(orderRequest,orderData);
						}
						orderData.put(ClientDataQueryConstants.LastModifiedTime,getStringDateTime(new Date()));
						orderData.put("workflowMessageId", System.currentTimeMillis());
						String message = ClientDataQueryHelper.getInstance().getOrderUpdateMessage(orderData,FXESPWorkflowLogger.getStringBuilder(),null);
						boolean notifyDealer = shouldNotifyDealer(orderRequest);
						if(notifyDealer)
						{
							if( log.isDebugEnabled() ) {
								log.debug("sendTrailingStopOrderUpdates : Notifying dealer order message=" + message + ", user=" +
										orderRequest.getUser().getShortName() + ", organization=" + orderRequest.getUser().getOrganization().getShortName());
							}
							sendMessageToDealer(message,jmsProps,orderRequest.getUser());
						}
						if( log.isDebugEnabled() ) {
							log.debug("sendTrailingStopOrderUpdates : Notifying chief dealer order message=" + message + ", organization=" + orderRequest.getUser().getOrganization().getShortName());
						}
						sendMessageToUserPermission( message, jmsProps, orderRequest.getUser().getOrganization(), ISConstantsC.CHIEFDEALER_MPVIEW_PERM );
						trailingStopOrder.getEntityDescriptor().setLastNotifiedStopPriceUpdateCount(stopPriceUpdateCount);
					}
				}
			}
		}
	}

	private void publishOrderView(OrderView ov) {
		//send to the jms proxy queue with the same key
		//replaces the older OV[if still not sent to the client] 
		//with the same key in the queue with this new OV.
		JMSProxyUtil.getInstance().addToJmsProxy(
				"OrderView:" + ov.getOrderId(), ORDER_BOOK_VIEW_MSG_PRIORITY,
				ORDER_BOOK_VIEW_HANDLER, ov, ov.getUser(), true);
	}

	final AtomicBoolean shutdown = new AtomicBoolean(false);
	@Override
	public void run() {
		log.info("run : START");
		long notificationSentTime = 0L;
		long trailingStopUpdateTime = 0L;
		while (!shutdown.get()) {
			try {
				AtomicCounter counter = OrderBookSubscriptionService.getInstance().getSubscriptionCounter();
				if (counter.get() == 0) {
					if( log.isDebugEnabled() ){
						log.debug("run : PARKING");
					}
					LockSupport.park(Thread.currentThread());
				}
				if( System.currentTimeMillis() - notificationSentTime > OrderBookViewAttributeMBeanC.getInstance().getOBVNotificationInterval()) {
					try {
						notifyAllSubscribedOrg();
					}
					catch (Exception ex){
						if( log.isDebugEnabled() ){
							log.debug("run : Exception ",ex);
						}
					}
					finally {
						notificationSentTime = System.currentTimeMillis();
					}
				}
				if(System.currentTimeMillis() - trailingStopUpdateTime > OrderBookViewAttributeMBeanC.getInstance().getTrailingStopTriggerUpdateNotificationInterval()){
					try {
						sendTrailingStopOrderUpdates();
					}
					catch (Exception ex){
						if( log.isDebugEnabled() ){
							log.debug("run : Exception ",ex);
						}
					}
					finally {
						trailingStopUpdateTime = System.currentTimeMillis();
					}
				}
				long parkTime = Math.min(OrderBookViewAttributeMBeanC.getInstance().getTrailingStopTriggerUpdatePersistenceInterval(),
						OrderBookViewAttributeMBeanC.getInstance().getOBVNotificationInterval());
				long parkUntil = System.currentTimeMillis()+parkTime;
				if( log.isDebugEnabled() ){
					log.debug("run : PARKING until "+new Date(parkUntil));
				}
				LockSupport.parkUntil(parkUntil);
				if( log.isDebugEnabled() ){
					log.debug("run : UNPARKED");
				}
			}
			catch (Exception exc) {
				log.error("OrderBookViewNotifier.run Exception " + exc.getMessage());
			}
		}
		log.info("run : SHUTDOWN");
	}
	
	private void logMessage(String message) {
		if(log.isInfoEnabled())
			log.info(message);
	}

	private boolean shouldNotifyDealer(OrderRequest order) {
		boolean isChiefDealer= order.getUser().hasPermission(ISConstantsC.CHIEFDEALER_MPVIEW_PERM) ||
				order.getUser().hasPermission(ISConstantsC.CHIEFDEALER_MPVIEW_PERM);
		return !isChiefDealer ;
	}

	private void sendMessageToDealer( String messageContents, Map messageProps, User user ) {
		try {
			List<String> userList = new ArrayList<String>();
			userList.add(user.getFullName());
			NotificationMessageSender.sendJMSMessageToSpecificUsers(messageContents, messageProps, userList);
		}
		catch ( Exception e ) {
			log.error( "sendMessageToUserPermission : Error in sending notification ",e );
		}
	}


	private void sendMessageToUserPermission( String messageContents, Map messageProps, Organization org, String permission ) {
		try {
			NotificationMessageSender.sendMessageToUserPermission(messageContents, messageProps, org, permission);
		}
		catch ( Exception e ) {
			log.error( "sendMessageToUserPermission : Error in sending notification ",e );
		}
	}

	protected static String getStringDateTime(java.util.Date date) {
		SimpleDateFormat dateTimeFormatter = new SimpleDateFormat(ISFactory.getInstance().getISMBean().getTradeExecutionDateTimeFormat());
		return dateTimeFormatter.format(date);
	}
}
