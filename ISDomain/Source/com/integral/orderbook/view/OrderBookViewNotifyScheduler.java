package com.integral.orderbook.view;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.LockSupport;


public class OrderBookViewNotifyScheduler {
    private OrderBookViewNotifier notifyTask;
	private Thread notifierThread;
	private final ThreadFactory threadFactory;

    private static final OrderBookViewNotifyScheduler instance = new OrderBookViewNotifyScheduler();

	public static OrderBookViewNotifyScheduler getInstance() {
		return instance;
	}

	private OrderBookViewNotifyScheduler() {
		this.threadFactory = new MyThreadFactory();
	}

	public synchronized void start() {
		if( notifyTask != null ){
			notifyTask.shutdown.set(true);
		}
		if( notifierThread != null ){
			LockSupport.unpark(notifierThread);
		}
		this.notifyTask = new OrderBookViewNotifier();
		this.notifierThread = threadFactory.newThread(notifyTask);
		this.notifierThread.start();
	}

	public synchronized void stop() {
		if( notifyTask != null ){
			notifyTask.shutdown.set(true);
		}
		if( notifierThread != null ){
			LockSupport.unpark(notifierThread);
		}
	}

	public void resume(){
		LockSupport.unpark(notifierThread);
	}
	
	private static class MyThreadFactory implements java.util.concurrent.ThreadFactory {
		final AtomicInteger threadNumber = new AtomicInteger(1);
		ThreadGroup tg = null;

		private MyThreadFactory() {
			this.tg = new ThreadGroup("OrderBookView");
		}

		public Thread newThread(Runnable runnable) {
			return new Thread(this.tg, runnable, "OrderBookView-"+ threadNumber.getAndIncrement());
		}
	}
}