package com.integral.orderbook.view;

import com.integral.system.configuration.IdcMBean;

public interface OrderBookViewAttributeMBean extends IdcMBean{
	
	String MBEAN_NAME = "com.integral.orderbook.view.OrderBookViewAttributeMBean";
	
	String ORDER_BOOK_VIEW_NOTIFICATION_INTERVAL = "Idc.OrderBook.View.NotificationInterval";
	String TRAILING_STOP_TRIGGER_UPDATE_NOTIFICATION_INTERVAL = "Idc.OrderBook.View.TrailingStopTriggerUpdateNotificationInterval";
	String TRAILING_STOP_TRIGGER_UPDATE_PERSISTENCE_INTERVAL = "Idc.OrderBook.View.TrailingStopTriggerUpdatePersistenceInterval";
	
	long getOBVNotificationInterval();

	long getTrailingStopTriggerUpdatePersistenceInterval();

	long getTrailingStopTriggerUpdateNotificationInterval();
}
