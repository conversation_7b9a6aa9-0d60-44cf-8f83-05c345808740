package com.integral.orderbook.view;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import com.integral.commons.counter.AtomicCounter;
import com.integral.util.collections.ConcurrentHashSet;
import org.json.JSONException;
import org.json.JSONStringer;

import com.integral.is.common.util.JMSProxyUtil;
import com.integral.is.oms.Order;
import com.integral.is.oms.descriptors.EntityDescriptor;
import com.integral.user.User;
import com.integral.jmsproxy.fxiapi.ApiCallback;
import com.integral.jmsproxy.fxiapi.ApiCommand;
import com.integral.jmsproxy.server.stream.StreamElement;
import com.integral.log.Log;
import com.integral.log.LogFactory;

public class OrderBookSubscriptionService {

	private static OrderBookSubscriptionService instance = new OrderBookSubscriptionService();

	private Log log = LogFactory.getLog(this.getClass().getName());

	private OrderBookSubscriptionService() {
	}

	public static OrderBookSubscriptionService getInstance() {
		return instance;
	}

	// Map containing list of users subscribed to orderbook view
	// key : <org_shortname>
	// value: <List of <user_shortname>>
	private ConcurrentHashMap<String, ConcurrentHashSet<String>> orgToSubscribedUsers = new ConcurrentHashMap<String, ConcurrentHashSet<String>>();

	// Cache for trailing stop orders
	// key: orderId
	// value: Order
	private final ConcurrentHashMap<String, Order> trailingStopOrdersCache = new ConcurrentHashMap<String, Order>();

	// Data cache for trailing stop orders
	// key: orderId
	// value: Map containing order data
	private final ConcurrentHashMap<String, Map<String, Object>> trailingStopOrdersDataCache = new ConcurrentHashMap<String, Map<String, Object>>();

	private final AtomicCounter subscriptionCounter = new AtomicCounter(0L);

	public AtomicCounter getSubscriptionCounter(){
		return subscriptionCounter;
	}

	public synchronized void registerUser(User user) {
		String orgKey = user.getOrganization().getShortName();
		ConcurrentHashSet<String> subscribedUsers = orgToSubscribedUsers.get(orgKey);
		if (subscribedUsers == null) {
			subscribedUsers = new ConcurrentHashSet<String>();
			ConcurrentHashSet<String> existingUsers = orgToSubscribedUsers.putIfAbsent(orgKey,subscribedUsers);
			if( existingUsers != null ){
				subscribedUsers = existingUsers;
			}
		}
        if (subscribedUsers.add(user.getShortName())) {
            logMessage("registerUser : New OrderBookView subscription request received from user:" + user.getShortName() + ":Org:" + orgKey);
            if (subscriptionCounter.incrementAndGet() == 1) {
                OrderBookViewNotifyScheduler.getInstance().start();
            }
			else{
				OrderBookViewNotifyScheduler.getInstance().resume();
			}
        } else {
            logMessage("registerUser : Duplicate OrderBookView subscription request received from user:" + user.getShortName() + ":Org:" + orgKey);
        }
    }

	public synchronized void deRegisterUser(User user) {
		String orgKey = user.getOrganization().getShortName();
		ConcurrentHashSet<String> subscribedUsers = orgToSubscribedUsers.get(orgKey);
		// ignore un-subscribe requests from users who have not subscribed
		if (subscribedUsers != null) {
			if( subscribedUsers.remove(user.getShortName())) {
				logMessage("deRegisterUser : OrderBookView unsubscription request received from user:" + user.getShortName() + ":Org:"+ orgKey);
				while (true){
					long currentCount = subscriptionCounter.get();
					long newCount = currentCount - 1;
					if( subscriptionCounter.compareAndSet(currentCount, newCount)){
						break;
					}
				}
				if( subscribedUsers.isEmpty() ){
					orgToSubscribedUsers.remove(orgKey);
				}
			}
		}
	}

	/**
	 * Returns a list of users subscribed to the order book view belonging to an
	 * org
	 * 
	 * @param orgKey
	 *            - Organization for which the subscribed users to be fetched
	 * @return List of users subscribed for the notifications ; null if none
	 */
	public ConcurrentHashSet<String> getSubscribedUsersForOrg(String orgKey) {
		return orgToSubscribedUsers.get(orgKey);
	}

	/**
	 * Returns a list of organizations with users registered for order book view
	 * notifications.
	 * 
	 * @return List of organizations
	 */
	public Enumeration<String> getOrganizationsWithActiveSubscriptions() {
		return orgToSubscribedUsers.keys();
	}

	private void logMessage(String message) {
		if (log.isInfoEnabled())
			log.info(message);
	}

	/**
	 * Adds a trailing stop order to the cache.
	 *
	 * @param order the trailing stop order to add
	 */
	public void addTrailingStopOrder(Order order) {
		if (order != null && order.getEntityDescriptor().isTrailingStopOrder()) {
			String orderId = order.getOrderId();
			trailingStopOrdersCache.put(orderId, order);
			if(subscriptionCounter.incrementAndGet() == 1){
				OrderBookViewNotifyScheduler.getInstance().start();
			}
			else{
				OrderBookViewNotifyScheduler.getInstance().resume();
			}
		}
	}

	/**
	 * Removes a trailing stop order from the cache.
	 *
	 * @param orderId the ID of the order to remove
	 * @return the removed order, or null if not found
	 */
	public Order removeTrailingStopOrder(String orderId) {
		// Remove from data cache as well
		trailingStopOrdersDataCache.remove(orderId);
		Order order = trailingStopOrdersCache.remove(orderId);
		if( order != null ){
			while (true){
				long currentCount = subscriptionCounter.get();
				long newCount = currentCount - 1;
				if( subscriptionCounter.compareAndSet(currentCount, newCount)){
					break;
				}
			}
		}
		return order;
	}

	/**
	 * Gets a trailing stop order from the cache.
	 *
	 * @param orderId the ID of the order to retrieve
	 * @return the order, or null if not found
	 */
	public Order getTrailingStopOrder(String orderId) {
		return trailingStopOrdersCache.get(orderId);
	}

	/**
	 * Returns the number of trailing stop orders in the cache.
	 *
	 * @return the size of the trailing stop orders cache
	 */
	public int getTrailingStopOrderCount() {
		return trailingStopOrdersCache.size();
	}

	/**
	 * Returns the trailing stop orders cache for iteration.
	 *
	 * @return the trailing stop orders cache
	 */
	public ConcurrentHashMap<String, Order> getTrailingStopOrdersCache() {
		return trailingStopOrdersCache;
	}

	/**
	 * Returns the trailing stop orders data cache.
	 *
	 * @return the trailing stop orders data cache
	 */
	public ConcurrentHashMap<String, Map<String, Object>> getTrailingStopOrdersDataCache() {
		return trailingStopOrdersDataCache;
	}

	/**
	 * Gets trailing stop order data from the data cache.
	 *
	 * @param orderId the ID of the order
	 * @return the order data map, or null if not found
	 */
	public Map<String, Object> getTrailingStopOrderData(String orderId) {
		return trailingStopOrdersDataCache.get(orderId);
	}

	public void setTrailingStopOrderData(String orderId, Map<String, Object> orderData) {
		trailingStopOrdersDataCache.put(orderId, orderData);
	}
}
