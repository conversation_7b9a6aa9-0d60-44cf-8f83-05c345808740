package com.integral.orderbook.view;

import com.integral.system.configuration.IdcMBeanC;

public class OrderBookViewAttributeMBeanC extends IdcMBeanC implements
		OrderBookViewAttributeMBean {

	public static OrderBookViewAttributeMBeanC mBeanInstance = new OrderBookViewAttributeMBeanC();
	private long trailingStopTriggerUpdatePersistenceInterval;
	private long trailingStopTriggerUpdateNotificationInterval;

	public static OrderBookViewAttributeMBeanC getInstance(){
		return mBeanInstance;
	}
	
	private OrderBookViewAttributeMBeanC(){
		super(MBEAN_NAME);
	}
	
	private long OBV_NOTIFICATION_INTERVAL;
	
	@Override
	public void initialize(){
		OBV_NOTIFICATION_INTERVAL = getLongProperty(ORDER_BOOK_VIEW_NOTIFICATION_INTERVAL, 100L);
		trailingStopTriggerUpdatePersistenceInterval = getLongProperty(TRAILING_STOP_TRIGGER_UPDATE_PERSISTENCE_INTERVAL, 60000);
		trailingStopTriggerUpdateNotificationInterval = getLongProperty(TRAILING_STOP_TRIGGER_UPDATE_NOTIFICATION_INTERVAL, 10000);
	}

	@Override
	public long getOBVNotificationInterval() {
		return OBV_NOTIFICATION_INTERVAL;
	}

	public long getTrailingStopTriggerUpdatePersistenceInterval() {
		return trailingStopTriggerUpdatePersistenceInterval;
	}

	public long getTrailingStopTriggerUpdateNotificationInterval() {
		return trailingStopTriggerUpdateNotificationInterval;
	}
}
