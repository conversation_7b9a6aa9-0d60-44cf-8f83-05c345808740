// Copyright (c) 2007-2008 Integral Development Corp. All rights reserved.
package com.integral.broker.aggregate;

import com.integral.aggregation.model.TierBasedSpread;
import com.integral.broker.BrokerAdaptorUtil;
import com.integral.broker.FacadePolicyC;
import com.integral.broker.model.*;
import com.integral.broker.model.enums.SpreadUnit;
import com.integral.broker.price.Price;
import com.integral.broker.price.PriceBook;
import com.integral.broker.price.PriceC;
import com.integral.broker.price.PriceFacade;
import com.integral.broker.price.PriceListC;
import com.integral.broker.price.PriceMapC;
import com.integral.broker.quote.QuoteFacade;
import com.integral.broker.sort.PriceComparatorFactory;
import com.integral.broker.util.ConfigurationUtil;
import com.integral.broker.cache.QuoteCacheFactory;
import com.integral.broker.cache.BrokerQuoteCache;
import com.integral.finance.currency.Currency;
import com.integral.finance.currency.CurrencyFactory;
import com.integral.finance.currency.CurrencyPair;
import com.integral.finance.dealing.Quote;
import com.integral.finance.dealing.fx.FXLegDealingPrice;
import com.integral.finance.fx.FXRateBasis;
import com.integral.is.ISCommonConstants;
import com.integral.is.common.cache.MDSFactory;
import com.integral.lp.quote.LGFXTick;
import com.integral.lp.quote.RateEventC;
import com.integral.math.MathUtil;
import com.integral.user.Organization;
import com.integral.user.OrganizationFunction;
import com.integral.util.Tuple;

import java.math.BigDecimal;
import java.util.*;

import static com.integral.broker.aggregate.AggregationStats.AggEvent.*;

import com.integral.broker.util.BrokerQuoteCacheUtil;

/**
 * Aggregates quotes using the <i>Price @ Size</i> algorithm, also known as <i>Multi Price - Tiers</i>. The aggregated
 * quote contains multi-tier prices conforming to the tier structure configured in the product. The worst price within
 * each tier is used as the tier price.
 * <p/>
 * Since this aggregator creates a multi-tier quote, it should only be used by a multi-tier organization.
 *
 * <AUTHOR> Development Corp.
 */
public class MultiTierWorstQuoteAggregatorC extends QuoteAggregatorC implements MultiTierQuoteAggregator {

    /**
     * Delegate contains price-spreading logic.
     */
    protected PriceSpreader spreader = new PriceSpreaderC();
    protected ESPPriceSpreaderC espPriceSpreader = new ESPPriceSpreaderC();

    protected PriceFacade priceFacade = FacadePolicyC.getInstance().getPriceFacade();
    protected QuoteFacade quoteFacade = FacadePolicyC.getInstance().getQuoteFacade();

    /**
     * Abbreviated Name
     */
    private static final String abbrName = "MTWA";

    /**
     * Aggregates a collection of provider quotes into a new quote using the <i>Price @ Size</i> algorithm.
     *
     *
     * @param quotes  to be aggregated
     * @param product which is being quoted
     * @param factory
     * @return aggregated quote
     */
    public PriceBook aggregate(Collection<Quote> quotes, Product product, QuoteAggregatorFactory factory) {
        factory.preAggregation(quotes);
        PriceBook book = null;
        Map<Integer,PriceBook> priceBookMap = new HashMap<Integer, PriceBook>();
        boolean isUseMultiTierRawBooks = false;
        if(product.isStreamCategoryCheckEnabled()) {
            /* For-each Tier, create a separate book since when creating raw book,
               the process merges prices based on rate which is not applicable in case of stream categories.
               Tier based stream category flags are based on tier sizes.
               It is okay to merge prices within applicable stream categories.*/
            isUseMultiTierRawBooks = true;     
            book = populateMultiTierRawBook(priceBookMap, quotes, product, factory);
            if(book == null) book = newPriceBook(factory);
        } else {
            isUseMultiTierRawBooks = false;
            book = createPriceBook( quotes, product, factory);
        }
        // create book of synthetic tiers & prices
        book = synthesize( book, priceBookMap, isUseMultiTierRawBooks, product, factory );
        book = applySkew(book, factory, product, false);
        book = round( book, product, factory);
        book = assignLPNames(book, factory);
        book = merge( book, factory );
        book = setMidRate(book, quotes, product);
        return book;
    }

    protected PriceBook  populateMultiTierRawBook(Map<Integer,PriceBook> priceBookMap , Collection<Quote> quotes, Product product, QuoteAggregatorFactory factory) {
        final Collection<? extends Tier> tiers = getTiers(product);
        PriceBook book = null;
        for (Tier tier : tiers) {
            if(!priceBookMap.containsKey(tier.getTierStreamCategoryFlags())) {
                Collection<Quote> filteredQuotes = new ArrayList<Quote>();
                for(Quote aQuote : quotes) {
                    if((tier.getTierStreamCategoryFlags() & aQuote.getStreamCategoryFlag()) == aQuote.getStreamCategoryFlag()) {
                        filteredQuotes.add(aQuote);
                    }
                }
                PriceBook tierBasedRawBook = createPriceBook(filteredQuotes, product, factory);
                priceBookMap.put(tier.getTierStreamCategoryFlags(), tierBasedRawBook);
                if(book == null) book = tierBasedRawBook;
            }
        }
        return book;
    }

    protected PriceBook assignLPNames(PriceBook book, QuoteAggregatorFactory factory) {
        Collection<Price> bids = book.getBids();
        Collection<Price> offers = book.getOffers();
        for (Price bid : bids) {
            bid.setLPName(factory.getBestBidPriceLPName());
        }
        for (Price offer : offers) {
            offer.setLPName(factory.getBestOfferPriceLPName());
        }

        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder(200);
            sb.append(this).append(".assignLPNames()");
            sb.append(' ').append("Best bid LP Name = ").append(factory.getBestBidPriceLPName());
            sb.append(' ').append("Best offer LP Name = ").append(factory.getBestOfferPriceLPName());
            log.debug(sb.toString());
        }
        return book;
    }

    protected void storeBestPricesLPNames(PriceBook book, Product product, QuoteAggregatorFactory factory) {
        factory.storeBestPriceLPName(book, product);
    }

    /**
     * Returns the abbreviated name for logging
     *
     * @return abbreviatedName
     */
    public String getAbbreviatedName() {
        return abbrName;
    }

    protected PriceBook createPriceBook(Collection<Quote> quotes, Product product, QuoteAggregatorFactory factory) {
        // create book to hold raw provider prices
        final PriceBook book = newPriceBook(factory);

        // determine base currency
        CurrencyPair ccyPair = product.getCurrencyPair();
        Currency ccy = ccyPair.getBaseCurrency();

        // get supported providers
        Configuration config = product.getConfiguration();
        Collection<Organization> allPriceProviders = BrokerAdaptorUtil.getInstance().getAllPriceProviders(config, product.getCurrencyPair());

        List<FXLegDealingPrice> orderPrices = null;

		AggregationStats stats = factory.getAggregationStats();
		if(stats != null && quotes.isEmpty()) stats.addEvent(EMPTY_QUOTE_CACHE);
		// populate bid book and offer book with provider prices
        for ( Quote quote : quotes ) {

            // skip invalid quotes
            if (!product.isMDFSourceEnabledForAggregation()) {
                if (!quoteFacade.isValid(quote, allPriceProviders, product)) {
                    if (stats != null) stats.addEvent(NONSTREAM_REFERENCE_INACTIVE_PROVIDER_QUOTE, quote);
                    continue;
                }
            }


            if (quote.getPriceType() == Quote.PRICE_TYPE_ORDERS) {
                if (orderPrices == null) {
                    orderPrices = new ArrayList<FXLegDealingPrice>();
                }

                orderPrices.addAll(quote.getBids());
                orderPrices.addAll(quote.getOffers());
                continue;
            }

            if ( quote.getPriceType() == Quote.PRICE_TYPE_MULTI_TIER ) {
                bookMultiTier(product, book, ccy, quote, factory);
            } else {
                bookSingleTierPrices(quote.getBids(), book, ccy, product, false, factory, quote);
                bookSingleTierPrices(quote.getOffers(), book, ccy, product, false, factory, quote);
            }
            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(200);
                sb.append(this).append(".createPriceBook(PRICE_TYPE_MULTI_TIER=1) ");
                sb.append("ccyPair: ").append(ccyPair.toString());
                sb.append(", quote.getPriceType(): ").append(quote.getPriceType());
                log.debug(sb.toString());
            }
        }

        // Add the order prices.
        if (orderPrices != null) {

            // Sort the order prices on time basis
            Collections.sort(orderPrices, PriceComparatorFactory.getInstance().getFxLegDealingPriceTimeComparator());
            bookSingleTierPrices( orderPrices, book, ccy, product, true, factory, null);
        }

        // Storing best price to drive the LP name in the final aggregated book.
        storeBestPricesLPNames(book, product, factory);
        
        return book;
    }

    private void bookMultiTier(Product product, PriceBook book, Currency ccy, Quote quote, QuoteAggregatorFactory factory) {
        double max1 = 0;
        double delta1;
		AggregationStats stats = factory.getAggregationStats();
        boolean isZeroSizePricesEnabled = product.isZeroSizePricesEnabled();
		for (FXLegDealingPrice dealingPrice : quote.getBids()) {
            if (priceFacade.isValid(dealingPrice, false,isZeroSizePricesEnabled)){
                double amount = dealingPrice.getDealtAmount();
                double rate = dealingPrice.getRate();
                if( ! isValidSize(amount, product,factory, dealingPrice) ) {
                	if(stats != null) stats.addEvent(LESS_THAN_MIN_AMOUNT_PRICE_ELEMENT, quote);
                    continue;
                }
                // log provider price, with currently available limit
                factory.getAggregatorLogger().logProviderPrice(dealingPrice, amount, ccy, product.getConfiguration().getStream().getShortName() );
                delta1 = Math.max( amount - max1, 0 );
                if(product.isMDFSourceEnabledForAggregation()&&!product.isSynthetic()){
                    addBid((LGFXTick) dealingPrice,book, rate, delta1);
                }else{
                    addBid(book, rate, delta1, dealingPrice);
                }
                max1 = Math.max(max1, amount);
            }else {
            	if(stats != null) {
            		if(dealingPrice.getDealtAmount() <= 0.0) stats.addEvent(ZERO_AMOUNT_PRICE_ELEMENT, quote);
            		if(!dealingPrice.isActive()) stats.addEvent(PRICE_ELEMENT_NOT_ACTIVE, quote);
            		if(dealingPrice.getRate() <= 0.0) stats.addEvent(ZERO_RATE_PRICE_ELEMENT, quote);
				}
			}
        }
        double max = 0;
        double delta;
        for (FXLegDealingPrice dealingPrice : quote.getOffers()) {
            if (priceFacade.isValid(dealingPrice, false,isZeroSizePricesEnabled)){
                double amount = dealingPrice.getDealtAmount();
                double rate = dealingPrice.getRate();
                if( ! isValidSize( amount ,product, factory, dealingPrice) ) {
                	if(stats != null) stats.addEvent(LESS_THAN_MIN_AMOUNT_PRICE_ELEMENT, quote);
                    continue;
                }
                // log provider price, with currently available limit
                factory.getAggregatorLogger().logProviderPrice(dealingPrice, amount, ccy, product.getConfiguration().getStream().getShortName() );
                delta = Math.max( amount - max, 0 );
                if(product.isMDFSourceEnabledForAggregation()&&!product.isSynthetic()) {
                    addOffer((LGFXTick)dealingPrice,book, rate, delta);
                }
                else{
                    addOffer(book, rate, delta, dealingPrice);
                }

                max = Math.max( max, amount );
            }else {
            	if(stats != null) {
            		if(dealingPrice.getDealtAmount() <= 0.0) stats.addEvent(ZERO_AMOUNT_PRICE_ELEMENT, quote);
            		if(!dealingPrice.isActive()) stats.addEvent(PRICE_ELEMENT_NOT_ACTIVE, quote);
            		if(dealingPrice.getRate() <= 0.0) stats.addEvent(ZERO_RATE_PRICE_ELEMENT, quote);
				}
			}
        }
    }

    protected PriceBook newPriceBook(QuoteAggregatorFactory factory) {
        if(factory.isHourglass() && !(factory instanceof BrokerQuoteAggregatorFactory)) return new PriceListC(factory);
        else return new PriceMapC(factory);
    }

    /**
     * Add single-tier prices to the price book.
     *
     * @param prices contains single-tier prices to populate price books
     * @param book  price book to be populated
     * @param ccy   currency unit of the limit amounts
     * @param factory
     */
    private void bookSingleTierPrices(final Collection<FXLegDealingPrice> prices, final PriceBook book, Currency ccy, Product product, boolean isOrderPrices, QuoteAggregatorFactory factory,
     Quote quote) {
        double rate;
        double amount;

        Tuple<Price,Price> bestBidOfferPrice = new Tuple<Price, Price>();
        if(isOrderPrices) {
            Collection<Price> bids = book.getBids();
            if(!bids.isEmpty()) {
                bestBidOfferPrice.first = bids.iterator().next();
            }
            Collection<Price> offers = book.getOffers();
            if(!offers.isEmpty()) {
                bestBidOfferPrice.second = offers.iterator().next();
            }
        }
		AggregationStats stats = factory.getAggregationStats();

        //TickC will be inactive if the limits are
        boolean isZeroSizePricesEnabled = product.isZeroSizePricesEnabled();

		for ( FXLegDealingPrice dp : prices ) {

            // skip invalid prices
            if ( !priceFacade.isValid(dp, false,isZeroSizePricesEnabled) ) {
            	if(stats != null) {
            		if(dp.getDealtAmount() <= 0.0) stats.addEvent(ZERO_AMOUNT_PRICE_ELEMENT, quote);
            		if(!dp.isActive()) stats.addEvent(PRICE_ELEMENT_NOT_ACTIVE, quote);
            		if(dp.getRate() <= 0.0) stats.addEvent(ZERO_RATE_PRICE_ELEMENT, quote);
				}
                continue;
            }

            // get the quoted rate
            rate = priceFacade.getRate(dp);

            // get the available limit, with units of standard base currency
            amount = priceFacade.getAmount(dp, ccy );

            if( ! isValidSize( amount, product, factory, dp ) ) {
            	if(stats != null) stats.addEvent(LESS_THAN_MIN_AMOUNT_PRICE_ELEMENT, quote);
                continue;
            }
            // log provider price, with currently available limit
            factory.getAggregatorLogger().logProviderPrice( dp, amount, ccy, product.getConfiguration().getStream().getShortName() );

            if (isOrderPrices) {
                //Drop Order prices that have no TTL.
                if(dp.getTTL() < 0) {
                    if(log.isDebugEnabled()) {
                        log.debug("MultiTierWorstQuoteAggregatorC.bookSingleTierPrices() : Dropping Order Price. TTL on Order quote->" + dp.getTTL());
                    }
                    continue;
                }

            }

            // add available limit to the total for the rate
            switch ( dp.getBidOfferMode() ) {
                case FXLegDealingPrice.BID:
                    addBid( book, rate, amount, dp );
                    break;
                case FXLegDealingPrice.OFFER:
                    addOffer( book, rate, amount, dp );
                    break;
            }
        }

        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 200 );
            sb.append( this ).append( ".bookSingleTierQuote " );
            sb.append( "{ dealing Prices = " ).append( prices );
            sb.append( ", book = " ).append( book );
            sb.append( ", ccy = " ).append( ccy );
            sb.append( '}' );
            log.debug( sb.toString() );
        }
    }
    

    /**
     * Create a price book consisting of synthetic tiers. Also spread each tier, and merge similar tiers.
     *
     *
     * @param raw     provider prices
     * @param product contains tier definitions and spreading parameters
     * @param factory
     * @return synthetic prices
     */
    private PriceBook synthesize(PriceBook raw, final Map<Integer, PriceBook> priceBooks, boolean useMultiTierRawBook, Product product, QuoteAggregatorFactory factory) {
        // get synthetic price tier definitions    	
        Configuration config = product.getConfiguration();
        Product underlyingProduct = null;
        boolean sorAggregationDifferFromUnderlying = false;
        int underlyingCcyPairSpotPrecision = -1;
        if(product.getVariableCurrency().isNonSpotSettlementType() && product.getConfiguration().isSorUsingMDSEnabled()){
        	underlyingProduct =  product.getConfiguration().getStream()
            		.getProduct(CurrencyFactory.getOriginalCurrencyPair(product.getCurrencyPair()));
        	config = underlyingProduct.getConfiguration();
			if (config.getQuoteAggregator() instanceof SingleTierQuoteAggregator) {
				sorAggregationDifferFromUnderlying = true;
			}else{
                underlyingCcyPairSpotPrecision = underlyingProduct.getRateBasis().getSpotPrecision();
            }
        }
        boolean doMin = config.isSpreadMinimumEnabled();
        boolean doMax = config.isSpreadMaximumEnabled() && ConfigurationUtil.allowMaxSpreadInVolatileMkt(config.getStream());
        Spread espSpread = config.getESPSpreadFacade();
        boolean doFixed = false;
        boolean doBSP = false;
        if (espSpread != null) {
            doFixed = espSpread.getType() == SpreadType.BIDOFFER;
            doBSP = espSpread.getType() == SpreadType.BPS;
        }
        boolean useEspSpreadInRfs = factory.doRFSESPMDSESPSpreading();
        boolean isBPS = config.getEspSpreadUnit() == SpreadUnit.BPS;
        double refRate = 0.0D;
		if (isBPS) {
			if (useEspSpreadInRfs) {
				refRate = factory.getReferenceRate();
			} else {
				refRate = underlyingProduct == null ? BrokerAdaptorUtil.getInstance().getLiveFXMDSMidRate(product.getCurrencyPair())
						: BrokerAdaptorUtil.getInstance().getLiveFXMDSMidRate(underlyingProduct.getCurrencyPair());
			}
		}
        
        if ( log.isDebugEnabled () )
        {
            log.debug ( new StringBuilder(200).append ( "Synthesize - refRate=" ).append( refRate ).toString());
        }

        final FXRateBasis rateBasis = product.getRateBasis();
        final Collection<? extends Tier> tiers = underlyingProduct == null ? getTiers(product) : getTiers(underlyingProduct);

        // create multi-tier price book
        final PriceBook cooked = new PriceListC(factory, tiers.size());
        cooked.setMultiTier(true);
        
        if(sorAggregationDifferFromUnderlying){
        	Date currentdate = new Date();
        	//Log for one second , every 5th minute of the hour
        	if(currentdate.getMinutes() == 5 && currentdate.getSeconds() == 2){
        		log.warn("SOR currency config aggregation methodology different from underlying currency pair config, SOR currency pair: "
        				+product.getCurrencyPair().getDisplayName()+" underlying currency pair: "+underlyingProduct.getCurrencyPair().getDisplayName());
        	}
        	return cooked;
        }

        // determine tier currency
        CurrencyPair ccyPair = product.getCurrencyPair();
        Currency tierCcy = config.getTierCurrency(ccyPair);
        Currency baseCcy = rateBasis.getBaseCurrency();
        Stream stream = config.getStream();
        OrganizationFunction orgFunction = stream.getBrokerOrganizationFunction() == null ? stream.getTakerOrganizationFunction() : stream.getBrokerOrganizationFunction();
        String org = orgFunction.getOrganization().getShortName();
        SpreadPriority spreadPriority = config.getSpreadPriority();

        // calculate the total amounts available in the book
        double totalBidAmount = getTotalAmount( raw.getBids() );
        double totalOfferAmount = getTotalAmount( raw.getOffers() );

        // check if there are any bids or offers to process
        boolean isBidContinue = !raw.getBids().isEmpty();
        boolean isOfferContinue = !raw.getOffers().isEmpty();

        // the following variables are declared outside the loop, so that they can be reused
        double bidLimit, offerLimit;
        Price bid, offer;
        RateMonotonizerC mono = factory.isHourglass() ? new DummyRateMonotonizerC() : new RateMonotonizerC();

        // populate synthetic price tiers
        MDSFactory mds = MDSFactory.getInstance();
        if (log.isDebugEnabled()) log.debug("synthesize");
        double bidOfferSpread=0.0;
        StringBuilder hourglassRateLogging = new StringBuilder(300).append("MultiTierWorstQuoteAggregatorC.hourglassRateLogging ")
                .append("CcyPair: ").append(ccyPair.getDisplayName()).append(", StreamName: ").append(stream.getName());
        int tierNumber = 1;
        for (Tier tier : tiers) {
            if (log.isDebugEnabled()) log.debug("synthesize tier: " + tier);
            // convert tier limits into base currency
            // calculate the total amounts available in the book
            if (useMultiTierRawBook) {
                raw = priceBooks.get(tier.getTierStreamCategoryFlags());
                totalBidAmount = getTotalAmount(raw.getBids());
                totalOfferAmount = getTotalAmount(raw.getOffers());
                isBidContinue = !raw.getBids().isEmpty();
                isOfferContinue = !raw.getOffers().isEmpty();
            }

            if (factory.isTermCcyAggregationSupported()) {
                bidLimit = tier.getBidLimit() == null ? totalBidAmount :
                        factory.getAmountOfBase(tier.getBidLimit());
                offerLimit = tier.getOfferLimit() == null ? totalOfferAmount :
                        factory.getAmountOfBase(tier.getOfferLimit());
            } else {
                bidLimit = tier.getBidLimit() == null ? totalBidAmount :
                        mds.convertBidAmount(org, tierCcy, baseCcy, tier.getBidLimit());
                offerLimit = tier.getOfferLimit() == null ? totalOfferAmount :
                        mds.convertOfferAmount(org, tierCcy, baseCcy, tier.getOfferLimit());
            }
            if (log.isDebugEnabled()) log.debug("synthesize b/o limit: " + bidLimit + " " + offerLimit);

            // calculate bid rate and amount for this tier, if previous bid tier has been filled
            bid = null;
            if (isBidContinue) {
                bid = getRate(raw.getBids(), bidLimit, factory);
                isBidContinue = bidLimit < totalBidAmount;
            }

            // calculate offer rate and amount for this tier, if previous offer tier has been filled
            offer = null;
            if (isOfferContinue) {
                offer = getRate(raw.getOffers(), offerLimit, factory);
                isOfferContinue = offerLimit < totalOfferAmount;
            }
            if (log.isDebugEnabled()) {
                StringBuilder sb = new StringBuilder(200);
                sb.append(", synthesize bo: ").append(bid).append(' ').append(isBidContinue)
                        .append(' ').append(offer).append(' ').append(isOfferContinue);
                log.debug(sb.toString());
            }
            if (bid != null && offer != null && bidOfferSpread == 0.0)
             bidOfferSpread=Math.abs(offer.getRate() - bid.getRate());
            if ( log.isDebugEnabled () )
            {
                log.debug ( new StringBuilder(200).append ( " MultiTierWorstQuoteAggregatorC bidOfferSpread for "+product.getCurrencyPair().getName()+" =" ).append( bidOfferSpread ).toString());
            }
            BrokerQuoteCacheUtil.addBidOfferSpreadToBrokerQuoteCache(bidOfferSpread,product);
            // spread tier
            if (SpreadPriority.First.equals(spreadPriority) && doFixed) {
                if (bid != null) applyBidSpread(rateBasis, bid, tier.getSpreadFixedBid(), config.getEspSpreadUnit(), refRate,bidOfferSpread);
                if (offer != null) applyOfferSpread(rateBasis, offer, tier.getSpreadFixedOffer(), config.getEspSpreadUnit(), refRate,bidOfferSpread);
            }

            if (bid != null && offer != null && doMin)
                spreader.spread(rateBasis, bid, offer, tier ,config.getEspSpreadUnit(),refRate,bidOfferSpread,underlyingCcyPairSpotPrecision );

            if (SpreadPriority.Default.equals(spreadPriority) && doFixed) {
                if (bid != null) applyBidSpread(rateBasis, bid, tier.getSpreadFixedBid(), config.getEspSpreadUnit(), refRate,bidOfferSpread);
                if (offer != null) applyOfferSpread(rateBasis, offer, tier.getSpreadFixedOffer(), config.getEspSpreadUnit(), refRate,bidOfferSpread);
            } else if (SpreadPriority.Default.equals(spreadPriority) && doBSP) {
                if (bid != null) BasisPointsSpreaderC.spreadBid(bid, tier.getSpreadFixedBid());
                if (offer != null) BasisPointsSpreaderC.spreadOffer(offer, tier.getSpreadFixedOffer());
            }
            if (bid != null && offer != null && doMax)
                PriceClipperC.clip(rateBasis, bid, offer, tier.getSpreadMaximum(), tier.getConfiguration().getSpreadMaximumBias(), config.getEspSpreadUnit(), refRate, bidOfferSpread,underlyingCcyPairSpotPrecision);

            // fixed phase can be done for 'matched' or un-matched prices.
            if (bid != null) {
// TODO query this makes mono be applied for even unmatched spreads - which old algo does not....
                hourglassRateLogging.append(", Tier").append(tierNumber++).append(", Current bid: ").append(bid.getRate());
                if (doMin || doMax) bid.setRate(mono.bid(bid.getRate()));
                hourglassRateLogging.append(", Bid for spreading: ").append(bid.getRate());
                if (SpreadPriority.Last.equals(spreadPriority) && doFixed)
                    applyBidSpread(rateBasis, bid, tier.getSpreadFixedBid(), config.getEspSpreadUnit(), refRate,bidOfferSpread);
                bid.setRate(PriceClipperC.roundSORWithSpotPrecision(bid.getRate(), true, underlyingCcyPairSpotPrecision));
                addBidToFor(bid, cooked, tier);
            } else {
                addBidToForUnFilledTier(cooked, tier);
            }

            if (offer != null) {
                hourglassRateLogging.append(", Current offer: ").append(offer.getRate());
                if (doMin || doMax) offer.setRate(mono.offer(offer.getRate()));
                hourglassRateLogging.append(", Offer for spreading: ").append(offer.getRate());
                if (SpreadPriority.Last.equals(spreadPriority) && doFixed)
                    applyOfferSpread(rateBasis, offer, tier.getSpreadFixedOffer(), config.getEspSpreadUnit(), refRate,bidOfferSpread);
                offer.setRate(PriceClipperC.roundSORWithSpotPrecision(offer.getRate(), false, underlyingCcyPairSpotPrecision));
                addOfferToFor(offer, cooked, tier);
            } else {
                addOfferToForUnFilledTier(cooked, tier);
            }
        }
        AggregationStats stats = factory.getAggregationStats();
        if (stats != null && cooked.getBids().isEmpty()) stats.addEvent(NOT_ENOUGH_BID_LIQUIDITY);
        if(stats != null && cooked.getOffers().isEmpty()) stats.addEvent(NOT_ENOUGH_OFFER_LIQUIDITY);

        if (log.isDebugEnabled()) {
            StringBuilder sb = new StringBuilder(200);
            sb.append(this).append(".synthesize");
            if (useMultiTierRawBook) {
                sb.append("{ rawbooks = ").append(priceBooks);
            } else {
                sb.append("{ raw = ").append(raw);
            }
            sb.append(", cooked = ").append(cooked);
            sb.append(", rateBasis = ").append(rateBasis);
            sb.append(", tiers = ").append(tiers);
            sb.append('}');
            log.debug(sb.toString());
            log.debug(hourglassRateLogging.toString());
        }
        return cooked;
    }
  
    private void applyBidSpread(FXRateBasis rateBasis, Price price, double spread, SpreadUnit spUnit, double refBidRate,double bidOfferSpread){
        if(spUnit == SpreadUnit.Pips){
            FixedPriceSpreaderC.spreadBid(rateBasis, price, spread);
        }else if(spUnit == SpreadUnit.BPS){
            price.setRate(price.getRate() - refBidRate * spread/TierBasedSpread.BASIS_POINTS_DIVISION_FACTOR);
        }else if(spUnit == SpreadUnit.Spread_Percent){
            price.setRate(price.getRate() - bidOfferSpread * spread/100.0);
        }
    }

    private void applyOfferSpread(FXRateBasis rateBasis, Price price, double spread, SpreadUnit spUnit, double refOfferRate,double bidOfferSpread){
        if(spUnit == SpreadUnit.Pips){
            FixedPriceSpreaderC.spreadOffer(rateBasis, price, spread);
        }else if(spUnit == SpreadUnit.BPS){
            price.setRate(price.getRate() + refOfferRate * spread/TierBasedSpread.BASIS_POINTS_DIVISION_FACTOR);
        }else if(spUnit == SpreadUnit.Spread_Percent){
            price.setRate(price.getRate() + bidOfferSpread * spread/100.0);
        }
    }
   
    protected void addOfferToForUnFilledTier(PriceBook cooked, Tier tier) {
        // If any extended aggregator wants to fill the prices and limits for unFilled tiers, can extend this method.
    }

    protected void addBidToForUnFilledTier(PriceBook cooked, Tier tier) {
        // If any extended aggregator wants to fill the prices and limits for unFilled tiers, can extend this method.
    }


    // enable subsclasses to override Tier derivation
    protected Collection<? extends Tier> getTiers(Product product) {
        Configuration configuration = product.getConfiguration();
        if(configuration.isMarketMaker()){
            return getSpreadTiersFromMMConfig(product.getCurrencyPair(), configuration);
        }else {
            return configuration.getTiersFacade();
        }
    }

    // allow subclasses to override adding Price to PriceBook for a Tier
    protected void addBidToFor(Price p, PriceBook pb, Tier t) {
        pb.addBid( p );
    }
    protected void addOfferToFor(Price p, PriceBook pb, Tier t) {
        pb.addOffer( p );
    }

    /**
     * Calculate the total amount available across a prices in a book.
     *
     * @param prices all of the available amounts at each price
     * @return the total amount available for a book
     */
    protected double getTotalAmount( final Collection<Price> prices ) {
        double total = 0;

        for ( Price price : prices ) {
            total += price.getAmount();
        }

        return total;
    }

    /**
     * Calculate the worst rate and amount available for a tier.
     *
     * @param prices all of the available amounts at each price
     * @param limit  maximum amount available for this tier
     * @return the amount available at the price for this tier
     */
    protected Price getRate( final Collection<Price> prices, final Double limit, QuoteAggregatorFactory factory) {
        Price worst = new PriceC( limit );

        for ( Price price : prices ) {
            // stop looping when tier limit has been filled
            if ( worst.isFull() ) {
                break;
            }

            // add the available amount at this price to the total amount for this tier
            worst.addAmount( price.getAmount() );

            // replace price for this tier with the worst rate
            worst.setRate( price.getRate() );

            // keep setting this flag - if the entire price was constructed using DO prices, these flags should be true.
            worst.setIsRateSourceDisplayedOrder(price.isRateSourceDisplayedOrder());
            worst.setIsRateSourcePeggedOrder(price.isRateSourcePeggedOrder());
//            if ( worst.isFull() ) break;
        }
        worst.setRate( worst.getRate() ); // throw exception on 'bad' rate
        worst.setRawRate( worst.getRate() );
        return worst;
    }

    private void replaceLastAmount( double amount, Collection<Price> prices ) {
        Price last = null;
        for ( Price price : prices ) {
            last = price;
        }
        if ( last != null ) {
            last.setAmount( amount );
        }
    }

    protected PriceBook round(PriceBook input, Product product, QuoteAggregatorFactory factory) {
        FXRateBasis rateBasis = product.getRateBasis();
        Collection<Price> bids = input.getBids();
        Collection<Price> offers = input.getOffers();
        for ( Price bid : bids ) {
            bid.setRate( MathUtil.round( bid.getRate(), rateBasis.getSpotPrecision(), BigDecimal.ROUND_FLOOR ) );
        }
        for ( Price offer : offers ) {
            offer.setRate( MathUtil.round( offer.getRate(), rateBasis.getSpotPrecision(), BigDecimal.ROUND_CEILING ) );
        }
        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 200 );
            sb.append( this ).append( ".round" );
            sb.append( ", cooked = " ).append(input);
            sb.append( ", rateBasis = " ).append( rateBasis );
            sb.append( '}' );
            log.debug( sb.toString() );
        }
        return input;
    }

    protected PriceBook merge(PriceBook raw, QuoteAggregatorFactory factory) {
        PriceBook cooked = new PriceListC(factory, raw.size() );
        cooked.setMultiTier( true );
        Iterator<Price> bids = raw.getBids().iterator();
        Iterator<Price> offers = raw.getOffers().iterator();
        Price prevBid = null;
        Price prevOffer = null;
        while ( bids.hasNext() && offers.hasNext() ) {
            Price bid = bids.next();
            Price offer = offers.next();
            if ( offer.isSameRate( prevOffer ) && bid.isSameRate( prevBid ) ) {
                // replace amounts in last bid and offer
                replaceLastAmount( bid.getAmount(), cooked.getBids() );
                replaceLastAmount( offer.getAmount(), cooked.getOffers() );
            } else {
                cooked.addBid( bid.getRate(), bid.getAmount(), bid.getLPName(), bid.isMultiProviderPrice(), bid.isRateSourceDisplayedOrder(), bid.isRateSourcePeggedOrder(), bid.getRawRate() );
                cooked.addOffer( offer.getRate(), offer.getAmount(), offer.getLPName(), offer.isMultiProviderPrice(), offer.isRateSourceDisplayedOrder(), offer.isRateSourcePeggedOrder(), offer.getRawRate());
            }
            prevBid = bid;
            prevOffer = offer;
        }
        while ( bids.hasNext() ) {
            Price bid = bids.next();
            if ( bid.isSameRate( prevBid ) ) {
                // replace amounts in last bid and offer
                replaceLastAmount( bid.getAmount(), cooked.getBids() );
            } else {
                cooked.addBid( bid.getRate(), bid.getAmount(), bid.getLPName(), bid.isMultiProviderPrice(), bid.isRateSourceDisplayedOrder(), bid.isRateSourcePeggedOrder(), bid.getRawRate() );
            }
            prevBid = bid;
        }
        while ( offers.hasNext() ) {
            Price offer = offers.next();
            if ( offer.isSameRate( prevOffer ) ) {
                // replace amounts in last bid and offer
                replaceLastAmount( offer.getAmount(), cooked.getOffers() );
            } else {
                cooked.addOffer( offer.getRate(), offer.getAmount(), offer.getLPName(), offer.isMultiProviderPrice(), offer.isRateSourceDisplayedOrder(), offer.isRateSourcePeggedOrder(), offer.getRawRate() );
            }
            prevOffer = offer;
        }
        if ( log.isDebugEnabled() ) {
            StringBuilder sb = new StringBuilder( 200 );
            sb.append( this ).append( ".merge" );
            sb.append( "{ raw = " ).append( raw );
            sb.append( ", cooked = " ).append( cooked );
            sb.append( '}' );
            log.debug( sb.toString() );
        }
        return cooked;
    }

    public Tier getTier( Configuration config, double amount, boolean isBid, CurrencyPair currencyPair) {
        List<Tier> tiers = config.isMarketMaker() ? getSpreadTiersFromMMConfig(currencyPair, config) : new ArrayList<Tier>( config.getTiersFacade() );
        return getTier( tiers, new PriceC( 0, amount, "" ), isBid );
    }

    // 'promoted' from MultiTierMarketQuoteAggregator
    // in 3.4.8 tier bid and offer limit are constrained to be ==

    protected Tier getTier( List<Tier> tiers, Price price, boolean isBid ) {
        Tier lastTier = null;
        for ( Tier tier : tiers ) {
            double limit = isBid ?
                    tier.getBidLimit() == null ? 0.0 : tier.getBidLimit()
                    : tier.getOfferLimit() == null ? 0.0 : tier.getOfferLimit();
            lastTier = tier;

            if ( limit >= price.getAmount() ) {
                return lastTier;
            }
        }
        return tiers.get( tiers.size() - 1 );
    }

    protected void addOffer( PriceBook book, double rate, double amount, FXLegDealingPrice dp ) {
        book.addOffer( rate, amount, dp.getQuote().getOrganization().getShortName(), false, dp.isRateSourceDisplayedOrder(), dp.isRateSourcePeggedOrder(), rate);
    }

    protected void addOffer(LGFXTick dp, PriceBook book, double rate, double amount) {
        book.addOffer( rate, amount, dp.getProviderOrg().getShortName(), false, dp.isRateSourceDisplayedOrder(), dp.isRateSourcePeggedOrder(), rate);
    }

    protected void addBid( PriceBook book, double rate, double amount, FXLegDealingPrice dp ) {
        book.addBid( rate, amount, dp.getQuote().getOrganization().getShortName(), false, dp.isRateSourceDisplayedOrder(), dp.isRateSourcePeggedOrder(), rate);
    }
    protected void addBid(LGFXTick dp, PriceBook book, double rate, double amount ) {
        book.addBid( rate, amount, dp.getProviderOrg().getShortName(), false, dp.isRateSourceDisplayedOrder(), dp.isRateSourcePeggedOrder(), rate);
    }

    //:todo move to common location
    protected boolean isValidSize( double amount, Product product,QuoteAggregatorFactory factory, FXLegDealingPrice price ) {
        //  check if amount is at least the minimum size
        return ( ( product.isZeroSizePricesEnabled() ? amount >= 0 : amount > 0 )
                && ( amount >= price.getQuote().getMinimumSize() )
                && ( amount >= factory.getMinAmountSize() )
                && ( amount >= factory.getMinQuoteSize() ) );
    }
    
    protected PriceBook setMidRate(PriceBook book, Collection<Quote> quotes, Product product){
    	//check for Spot And Mds
    	boolean isSpotMds = product.getProperty(ISCommonConstants.ESP_RFS) != null && (Boolean)product.getProperty(ISCommonConstants.ESP_RFS);
		if (isSpotMds) {
			Set<Organization> quoteOrgs = new HashSet<Organization>();
			
			for (Quote quote : quotes) {
				quoteOrgs.add(quote.getOrganization());
			}
			
			//If only 1 provider exist in the QuoteCache and quote provider is Broker then use that midRate
			if(quoteOrgs.size() == 1){
				Iterator<Quote> quoteIterator = quotes.iterator();
				if(quoteIterator.hasNext()){
					Quote quote = quoteIterator.next();
					Organization realQuoteOrg = quote.getOrganization().getRealLP() != null ? quote.getOrganization().getRealLP() : quote.getOrganization();
					if(quote instanceof RateEventC && realQuoteOrg.isBroker()){
						Double midRate = ((RateEventC)quote).getMidRate();
						book.setMidRate(midRate);
					}
				}
			}
			
			if(log.isDebugEnabled()){
    			StringBuilder sb = new StringBuilder();
    			sb.append(this).append(".setMidRate - ");
    			sb.append(" cp=").append(product.getCurrencyPair().getName());
    			sb.append(" espRfs=").append(true);
    			sb.append(", quoteOrg=[");
    			for (Quote quote : quotes) {
    				sb.append(quote.getOrganization().getShortName()).append(", ");
				}
    			sb.append("]");
    			sb.append(", org=").append(product.getBroker() != null ? product.getBroker().getShortName() : "");
    			sb.append(", midRate=").append(book.getMidRate() != null ? book.getMidRate() : "");
    			log.debug(sb.toString());
    		}
        	
        	
    	}else{
    		Price bid = null;
    		Price offer = null;
    		if(book.getMidRate() == null){
        		Iterator<Price> bidIterator = book.getBids().iterator();
            	Iterator<Price> offerIterator = book.getOffers().iterator();
                if(bidIterator.hasNext() && offerIterator.hasNext()){
                	bid = bidIterator.next();
                	offer = offerIterator.next();
                	double mid = (bid.getRawRate() + offer.getRawRate())/2;
                	int midRatePrecision = product.getRateBasis().getSpotPrecision() + 1;
                	mid = MathUtil.round(mid, midRatePrecision, BigDecimal.ROUND_HALF_UP);
                	book.setMidRate(mid);
                }
        	}
            
            if(log.isDebugEnabled()){
    			StringBuilder sb = new StringBuilder();
    			sb.append(this).append(".setMidRate - ");
    			sb.append(" cp=").append(product.getCurrencyPair().getName());
    			sb.append(" esp=").append(true);
    			sb.append(", org=").append(product.getBroker() != null ? product.getBroker().getShortName() : "");
    			sb.append(", bidRate=").append(bid!=null? bid.getRate():"");
    			sb.append(", offerRate=").append(offer!=null ? offer.getRate(): "");
    			sb.append(", bidRateRate=").append(bid!=null ? bid.getRawRate() :"");
    			sb.append(", offerRateRate=").append(offer!=null ? offer.getRawRate() :"");
    			sb.append(", midRate=").append(book.getMidRate());
    			log.debug(sb.toString());
    		}
    		
    	}
    	
    	return book;
    }

}
